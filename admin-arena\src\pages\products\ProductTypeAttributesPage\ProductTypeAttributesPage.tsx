// Product Type-Attributes Association Page
// Inspired by Django template for managing product type attributes

import React, { useState, useMemo } from 'react'
import { FiPlus, FiTrash2, FiEdit2, FiSave, FiX, FiSettings } from 'react-icons/fi'
import Select from 'react-select'
import { useF<PERSON>, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import {
  useProductTypeAttributes,
  useAssociateProductTypeAttributes,
  useUpdateProductTypeAttribute,
  useRemoveProductTypeAttribute
} from '../../../hooks/use-products'
import {useProductTypes} from '../../../hooks/products-hooks/use-product-types'
import {useAttributes} from '../../../hooks/products-hooks/use-attributes'
import { Card, CardHeader, CardBody } from '../../../components/ui/Card'
import { Button } from '../../../components/ui/Button'
import { Switch } from '../../../components/ui/Switch'
import { Badge } from '../../../components/ui/Badge'
import { PageLoading } from '../../../components/ui/LoadingSpinner'
import { PermissionGuard } from '../../../components/auth/AuthGuard'
import type { ProductTypeAttribute, ProductTypeAttributeAssociation } from '../../../types/api-types'
import styles from './ProductTypeAttributesPage.module.scss'

// Zod schema for association form
const associationSchema = z.object({
  attributes: z.array(z.object({
    attribute_id: z.number().min(1, 'Please select an attribute'),
    is_filterable: z.boolean(),
    is_option_selector: z.boolean(),
  })).min(1, 'Please add at least one attribute'),
})

type AssociationFormData = z.infer<typeof associationSchema>

export const ProductTypeAttributesPage: React.FC = () => {
  const [selectedProductTypeId, setSelectedProductTypeId] = useState<number | null>(null)
  const [editingAttributeId, setEditingAttributeId] = useState<number | null>(null)
  const [newAttributes, setNewAttributes] = useState<ProductTypeAttributeAssociation[]>([])

  // API hooks
  const { data: productTypes } = useProductTypes()
  const { data: attributes } = useAttributes()
  const { data: existingAttributes, isLoading: isLoadingAttributes } = useProductTypeAttributes(
    selectedProductTypeId || 0
  )
  const associateAttributesMutation = useAssociateProductTypeAttributes()
  const updateAttributeMutation = useUpdateProductTypeAttribute()
  const removeAttributeMutation = useRemoveProductTypeAttribute()

  // Form setup
  const form = useForm<AssociationFormData>({
    resolver: zodResolver(associationSchema),
    defaultValues: {
      attributes: [],
    },
  })

  // Get available attributes (not already associated)
  const availableAttributes = useMemo(() => {
    if (!attributes || !existingAttributes) return attributes || []
    
    const existingAttributeIds = existingAttributes.map(ea => ea.attribute)
    const newAttributeIds = newAttributes.map(na => na.attribute_id)
    const usedAttributeIds = [...existingAttributeIds, ...newAttributeIds]
    
    return attributes.filter(attr => !usedAttributeIds.includes(attr.id))
  }, [attributes, existingAttributes, newAttributes])

  // Product type options for react-select
  const productTypeOptions = productTypes?.map(type => ({
    value: type.id,
    label: type.title,
  })) || []

  // Attribute options for react-select
  const attributeOptions = availableAttributes.map(attr => ({
    value: attr.id,
    label: attr.title,
  }))

  // Handle product type selection
  const handleProductTypeChange = (option: any) => {
    setSelectedProductTypeId(option ? option.value : null)
    setNewAttributes([])
    setEditingAttributeId(null)
    form.reset()
  }

  // Add new attribute row
  const addNewAttributeRow = () => {
    setNewAttributes(prev => [...prev, {
      attribute_id: 0,
      is_filterable: false,
      is_option_selector: false,
    }])
  }

  // Remove new attribute row
  const removeNewAttributeRow = (index: number) => {
    setNewAttributes(prev => prev.filter((_, i) => i !== index))
  }

  // Update new attribute
  const updateNewAttribute = (index: number, field: keyof ProductTypeAttributeAssociation, value: any) => {
    setNewAttributes(prev => prev.map((attr, i) => 
      i === index ? { ...attr, [field]: value } : attr
    ))
  }

  // Save new associations
  const saveNewAssociations = async () => {
    if (!selectedProductTypeId || newAttributes.length === 0) return

    const validAttributes = newAttributes.filter(attr => attr.attribute_id > 0)
    if (validAttributes.length === 0) return

    try {
      await associateAttributesMutation.mutateAsync({
        productTypeId: selectedProductTypeId,
        attributes: validAttributes,
      })
      setNewAttributes([])
    } catch (error) {
      // Error is handled by the mutation hook
    }
  }

  // Handle update existing attribute
  const handleUpdateAttribute = async (
    attributeId: number, 
    data: Partial<ProductTypeAttributeAssociation>
  ) => {
    if (!selectedProductTypeId) return

    try {
      await updateAttributeMutation.mutateAsync({
        productTypeId: selectedProductTypeId,
        attributeId,
        data,
      })
      setEditingAttributeId(null)
    } catch (error) {
      // Error is handled by the mutation hook
    }
  }

  // Handle remove attribute
  const handleRemoveAttribute = async (attributeId: number) => {
    if (!selectedProductTypeId) return
    
    if (window.confirm('Are you sure you want to remove this attribute from the product type?')) {
      try {
        await removeAttributeMutation.mutateAsync({
          productTypeId: selectedProductTypeId,
          attributeId,
        })
      } catch (error) {
        // Error is handled by the mutation hook
      }
    }
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.titleSection}>
          <h1 className={styles.title}>Product Type Attributes</h1>
          <p className={styles.subtitle}>
            Associate attributes with product types and configure their properties
          </p>
        </div>
      </div>

      {/* Product Type Selection */}
      <Card className={styles.selectionCard}>
        <CardHeader>
          <h3>📋 Select Product Type</h3>
        </CardHeader>
        <CardBody>
          <div className={styles.selectionForm}>
            <div className={styles.formGroup}>
              <label htmlFor="productType" className={styles.label}>
                Choose a product type to view and manage its attribute associations
              </label>
              <Select
                id="productType"
                options={productTypeOptions}
                placeholder="Select a product type..."
                classNamePrefix="react-select"
                value={productTypeOptions.find(opt => opt.value === selectedProductTypeId) || null}
                onChange={handleProductTypeChange}
                styles={{ menu: base => ({ ...base, zIndex: 9999 }) }}
              />
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Associations Container */}
      {selectedProductTypeId && (
        <div className={styles.associationsContainer}>
          {/* Existing Associations */}
          <Card className={styles.existingCard}>
            <CardHeader>
              <h4>🔗 Existing Associations</h4>
            </CardHeader>
            <CardBody>
              {isLoadingAttributes ? (
                <PageLoading message="Loading attributes..." />
              ) : existingAttributes && existingAttributes.length > 0 ? (
                <div className={styles.attributesList}>
                  {existingAttributes.map((attr) => (
                    <div key={attr.id} className={styles.attributeRow}>
                      <div className={styles.attributeInfo}>
                        <Badge variant="primary">{attr.attribute_title}</Badge>
                      </div>
                      <div className={styles.attributeSettings}>
                        <div className={styles.settingItem}>
                          <label>Filterable</label>
                          <Switch
                            checked={attr.is_filterable}
                            onChange={(e) => handleUpdateAttribute(attr.attribute, {
                              is_filterable: e.target.checked
                            })}
                            disabled={updateAttributeMutation.isPending}
                          />
                        </div>
                        <div className={styles.settingItem}>
                          <label>Option Selector</label>
                          <Switch
                            checked={attr.is_option_selector}
                            onChange={(e) => handleUpdateAttribute(attr.attribute, {
                              is_option_selector: e.target.checked
                            })}
                            disabled={updateAttributeMutation.isPending}
                          />
                        </div>
                      </div>
                      <div className={styles.attributeActions}>
                        <PermissionGuard permission="staff.delete_producttypeattribute">
                          <Button
                            variant="danger"
                            size="sm"
                            onClick={() => handleRemoveAttribute(attr.attribute)}
                            disabled={removeAttributeMutation.isPending}
                          >
                            <FiTrash2 />
                          </Button>
                        </PermissionGuard>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className={styles.noAssociations}>
                  No existing associations found.
                </div>
              )}
            </CardBody>
          </Card>

          {/* New Associations */}
          <Card className={styles.newCard}>
            <CardHeader>
              <div className={styles.newCardHeader}>
                <h4>➕ Add New Associations</h4>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={addNewAttributeRow}
                  disabled={availableAttributes.length === 0}
                >
                  <FiPlus />
                  Add Attribute
                </Button>
              </div>
            </CardHeader>
            <CardBody>
              {newAttributes.length > 0 ? (
                <div className={styles.newAttributesList}>
                  {newAttributes.map((attr, index) => (
                    <div key={index} className={styles.newAttributeRow}>
                      <div className={styles.attributeSelect}>
                        <Select
                          options={attributeOptions}
                          placeholder="Select attribute..."
                          classNamePrefix="react-select"
                          value={attributeOptions.find(opt => opt.value === attr.attribute_id) || null}
                          onChange={opt => updateNewAttribute(index, 'attribute_id', opt ? opt.value : 0)}
                          styles={{ menu: base => ({ ...base, zIndex: 9999 }) }}
                        />
                      </div>
                      <div className={styles.attributeSettings}>
                        <div className={styles.settingItem}>
                          <label>Filterable</label>
                          <Switch
                            checked={attr.is_filterable}
                            onChange={(e) => updateNewAttribute(index, 'is_filterable', e.target.checked)}
                          />
                        </div>
                        <div className={styles.settingItem}>
                          <label>Option Selector</label>
                          <Switch
                            checked={attr.is_option_selector}
                            onChange={(e) => updateNewAttribute(index, 'is_option_selector', e.target.checked)}
                          />
                        </div>
                      </div>
                      <div className={styles.attributeActions}>
                        <Button
                          variant="danger"
                          size="sm"
                          onClick={() => removeNewAttributeRow(index)}
                        >
                          <FiX />
                        </Button>
                      </div>
                    </div>
                  ))}
                  <div className={styles.saveActions}>
                    <Button
                      variant="primary"
                      onClick={saveNewAssociations}
                      disabled={associateAttributesMutation.isPending || newAttributes.every(attr => attr.attribute_id === 0)}
                    >
                      <FiSave />
                      {associateAttributesMutation.isPending ? 'Saving...' : 'Save Associations'}
                    </Button>
                  </div>
                </div>
              ) : (
                <div className={styles.noNewAttributes}>
                  Click "Add Attribute" to start associating attributes with this product type.
                </div>
              )}
            </CardBody>
          </Card>
        </div>
      )}
    </div>
  )
}
